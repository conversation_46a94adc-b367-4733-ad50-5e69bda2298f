#!/bin/bash

# Pod库优化工具 - 无引用图片检测模块
# 功能: 检测Pod中无引用的图片文件，支持并发处理和动画序列帧检测
# 用法: source "$SCRIPT_DIR/lib/unused_detector.sh"
# 依赖: ripgrep/grep, common.sh, database.sh
# 版本: v1.0

# 引入依赖
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# 现代化进度条显示（类似 yarn install）
show_modern_progress() {
    local current="$1"
    local total="$2" 
    local message="${3:-扫描图片}"
    
    # Spinner 字符（Unicode Braille patterns）
    local spinner_chars="⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
    local spinner_index=$(( ($(date +%s%N) / 100000000) % 10 ))
    local spinner_char="${spinner_chars:$spinner_index:1}"
    
    if [ "$total" -gt 0 ]; then
        local percent=$(( current * 100 / total ))
        local bar_width=30
        local filled=$(( percent * bar_width / 100 ))
        local empty=$(( bar_width - filled ))
        
        # 构建进度条
        local progress_bar=""
        for i in $(seq 1 $filled); do 
            progress_bar+="█"
        done
        for i in $(seq 1 $empty); do 
            progress_bar+="░"
        done
        
        printf "\r  %s %s [%s] %d%% (%d/%d)" \
            "$spinner_char" "$message" "$progress_bar" "$percent" "$current" "$total"
    else
        printf "\r  %s %s..." "$spinner_char" "$message"
    fi
}

# 动态进度条（用于不确定进度的任务）
show_dynamic_progress() {
    local message="${1:-处理中}"
    local i=0
    local spinner_chars="⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
    local length=${#spinner_chars}
    
    while true; do
        local pos=$((i % length))
        local spinner_char="${spinner_chars:$pos:1}"
        printf "\r  %s %s..." "$spinner_char" "$message"
        sleep 0.1
        i=$((i + 1))
    done
}

# 向后兼容的旧函数名
show_progress() {
    show_dynamic_progress "$@"
}

# 检测是否为动画序列帧
is_animation_sequence_frame() {
    local file_path="$1"
    local pod_dir="$2"
    local filename=$(basename "$file_path")
    local basename_no_ext=$(echo "$filename" | sed 's/\.[^.]*$//')

    # 检测常见的动画序列帧命名模式
    # 如: ani_good1, ani_good2, ..., ani_good12
    # 或: frame_01, frame_02, ..., frame_30
    # 或: loading_1, loading_2, ..., loading_8
    # 或: img_boom_00000, img_boom_00001, ..., img_boom_00027 (动态生成)
    local base_pattern=""
    local sequence_number=""

    # 方法1: 检测动态文件名生成模式
    # 搜索代码中是否有该文件名作为前缀的字符串常量定义
    if check_dynamic_animation_reference "$basename_no_ext" "$pod_dir"; then
        return 0
    fi

    # 方法2: 改进的文件名模式检测 - 支持各种字符+数字组合
    # 使用更宽泛的正则：字符(字母、数字、下划线) + 最后的数字序列 + 可选分辨率后缀
    # 这样可以处理 img_boom_000 这种前缀包含数字的情况
    if [[ "$basename_no_ext" =~ ^(.+[_-])([0-9]+)(@[0-9]x)?$ ]]; then
        local prefix="${BASH_REMATCH[1]}"
        local number="${BASH_REMATCH[2]}"
        local resolution_suffix="${BASH_REMATCH[3]}"

        # 在代码中搜索动画相关的用法模式
        # 1. 搜索循环加载序列帧的代码模式
        if grep -r -l --include="*.m" --include="*.swift" --include="*.mm" --exclude="Contents.json" \
           -E "(for|NSInteger|Int).*[0-9]+.*${prefix}|animationImages.*${prefix}|${prefix}.*images" \
           "$pod_dir" >/dev/null 2>&1; then
            echo "🎬 动画序列帧: $basename_no_ext (代码循环模式)"
            return 0
        fi

        # 2. 搜索字符串格式化的模式 stringWithFormat:@"${prefix}%
        if grep -r -l --include="*.m" --include="*.swift" --include="*.mm" --exclude="Contents.json" \
           -E "stringWithFormat.*${prefix}.*%|String.*format.*${prefix}" \
           "$pod_dir" >/dev/null 2>&1; then
            echo "🎬 动画序列帧: $basename_no_ext (字符串格式化模式)"
            return 0
        fi

        # 3. 检查是否有其他序列帧文件存在（至少3个连续编号）
        local current_num=$((10#$number))  # 强制十进制
        local sequence_count=0
        local found_files=()

        # 检查范围：current_num-2 到 current_num+2
        for i in $(seq $((current_num-2)) $((current_num+2))); do
            if [ $i -le 0 ]; then continue; fi

            # 保持原始数字的位数格式
            local formatted_num=$(printf "%0${#number}d" $i)
            local test_filename="${prefix}${formatted_num}${resolution_suffix}"

            # 在pod目录中查找匹配的文件
            local found_file=$(find "$pod_dir" -name "${test_filename}.*" -type f 2>/dev/null | head -1)
            if [ -n "$found_file" ]; then
                sequence_count=$((sequence_count + 1))
                found_files+=("$(basename "$found_file")")
            fi
        done

        # 如果找到3个或以上连续编号的文件，认为是序列帧
        if [ $sequence_count -ge 3 ]; then
            echo "🎬 动画序列帧: $basename_no_ext (文件序列模式，数量: $sequence_count)"
            return 0
        fi
    fi

    return 1
}

# 检测系统图标引用（避免误删系统图标）
check_system_icon_reference() {
    local filename="$1"
    local pod_dir="$2"
    local basename_no_ext=$(echo "$filename" | sed 's/\.[^.]*$//')

    # 常见的系统图标模式
    local system_icon_patterns=(
        "Image(systemName:.*$basename_no_ext"
        "UIImage(systemName:.*$basename_no_ext"
        "systemImageNamed:.*$basename_no_ext"
    )

    for pattern in "${system_icon_patterns[@]}"; do
        if grep -r -l --include="*.swift" --include="*.m" --include="*.h" --include="*.mm" \
           -E "$pattern" "$pod_dir" >/dev/null 2>&1; then
            echo "🍎 系统图标引用: $filename (模式: $pattern)"
            return 0
        fi
    done

    return 1
}

# 检测编译时生成的代码引用（如R.swift, SwiftGen等）
check_generated_code_reference() {
    local filename="$1"
    local pod_dir="$2"
    local basename_no_ext=$(echo "$filename" | sed 's/\.[^.]*$//')
    local clean_name=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//' | sed 's/_normal$//' | sed 's/_selected$//' | sed 's/_hover$//' | sed 's/_pressed$//' | sed 's/_disabled$//' | sed 's/_highlighted$//')

    # 检测R.swift生成的代码模式
    # 如: R.image.iconName, R.image.icon_name
    local r_swift_patterns=(
        "R\.image\.$clean_name"
        "R\.image\.$basename_no_ext"
        "R\.image\.$(echo "$clean_name" | sed 's/_//g')"  # 去掉下划线的驼峰命名
    )

    # 检测SwiftGen生成的代码模式
    # 如: Asset.iconName.image, UIImage(asset: .iconName)
    local swiftgen_patterns=(
        "Asset\.$clean_name"
        "Asset\.$basename_no_ext"
        "UIImage(asset:.*$clean_name"
        "UIImage(asset:.*$basename_no_ext"
    )

    # 检测其他代码生成工具模式
    local other_patterns=(
        "Images\.$clean_name"
        "Images\.$basename_no_ext"
        "ImageAssets\.$clean_name"
        "ImageAssets\.$basename_no_ext"
    )

    # 合并所有模式
    local all_patterns=("${r_swift_patterns[@]}" "${swiftgen_patterns[@]}" "${other_patterns[@]}")

    for pattern in "${all_patterns[@]}"; do
        if grep -r -l --include="*.swift" --include="*.m" --include="*.h" --include="*.mm" \
           -E "$pattern" "$pod_dir" >/dev/null 2>&1; then
            echo "🔧 编译时生成代码引用: $filename (模式: $pattern)"
            return 0
        fi
    done

    return 1
}

# 检测动态动画引用（如通过stringWithFormat生成的文件名）
check_dynamic_animation_reference() {
    local filename="$1"
    local pod_dir="$2"

    # 只检测明显的序列帧模式（至少3位数字）
    if ! [[ "$filename" =~ [0-9]{3,} ]]; then
        return 1
    fi

    # 尝试不同长度的前缀来匹配动态生成的模式
    # 例如: img_boom_00000 -> 检查 img_boom_000, img_boom_00, img_boom_0 等前缀
    local len=${#filename}

    # 从最长到最短尝试不同的前缀长度
    for prefix_len in $(seq $((len-1)) -1 8); do
        local prefix="${filename:0:$prefix_len}"

        # 跳过太短的前缀
        if [ ${#prefix} -lt 8 ]; then
            continue
        fi

        # 检查该前缀是否在代码中被定义为字符串常量
        # 匹配模式如: @"img_boom_000", rippleImagePre = @"img_boom_000"
        local prefix_found=false
        if grep -r -l --include="*.m" --include="*.swift" --include="*.mm" --exclude="Contents.json" \
           -E "@\"${prefix}\"|= @\"${prefix}\"" \
           "$pod_dir" >/dev/null 2>&1; then
            prefix_found=true
        fi

        if [ "$prefix_found" = true ]; then
            # 进一步检查是否有格式化字符串的使用
            # 匹配模式如: stringWithFormat:@"%@%02d", [NSString stringWithFormat:@"%@%d"
            if grep -r -l --include="*.m" --include="*.swift" --include="*.mm" --exclude="Contents.json" \
               -E "stringWithFormat.*%@.*%[0-9]*d" \
               "$pod_dir" >/dev/null 2>&1; then

                # 检查是否有循环或数组操作
                if grep -r -l --include="*.m" --include="*.swift" --include="*.mm" --exclude="Contents.json" \
                   -E "for.*[0-9]+.*rippleImageCount|NSMutableArray.*images|addObject.*name" \
                   "$pod_dir" >/dev/null 2>&1; then
                    return 0
                fi
            fi
        fi
    done

    # 特殊检测：检查是否有多个相似的序列文件存在
    # 这可以作为动态生成的间接证据
    if check_sequence_file_pattern "$filename" "$pod_dir"; then
        return 0
    fi

    return 1
}

# 检查序列文件模式 - 改进版：支持英文字符+数字的组合
check_sequence_file_pattern() {
    local filename="$1"
    local pod_dir="$2"

    # 使用更宽泛的正则：字符(字母、数字、下划线) + 最后的数字序列 + 可选分辨率后缀
    # 这样可以处理 img_boom_000 这种前缀包含数字的情况
    if [[ "$filename" =~ ^(.+[_-])([0-9]+)(@[0-9]x)?$ ]]; then
        local prefix="${BASH_REMATCH[1]}"
        local number="${BASH_REMATCH[2]}"
        local resolution_suffix="${BASH_REMATCH[3]}"

        # 检查递增规律：当前数字前后各2个数字
        local current_num=$((10#$number))  # 强制十进制
        local sequence_count=0
        local found_files=()

        # 检查范围：current_num-2 到 current_num+2
        for i in $(seq $((current_num-2)) $((current_num+2))); do
            if [ $i -le 0 ]; then continue; fi

            # 保持原始数字的位数格式
            local formatted_num=$(printf "%0${#number}d" $i)
            local test_filename="${prefix}${formatted_num}${resolution_suffix}"

            # 在pod目录中查找匹配的文件
            local found_file=$(find "$pod_dir" -name "${test_filename}.*" -type f 2>/dev/null | head -1)
            if [ -n "$found_file" ]; then
                sequence_count=$((sequence_count + 1))
                found_files+=("$(basename "$found_file")")
            fi
        done

        # 如果找到3个或以上连续编号的文件，认为是序列帧
        if [ $sequence_count -ge 3 ]; then
            # 🎬 显著提示序列帧检测结果（简化输出，避免控制台混乱）
            echo "🎬 动画序列帧: $filename (序列数量: $sequence_count)"
            return 0
        fi
    fi

    return 1
}

# 单个图片引用检测（用于并发处理）
check_single_image_reference() {
    local file="$1"
    local pod_dir="$2"
    local result_file="$3"
    
    if [ ! -f "$file" ]; then
        return 0
    fi
    
    local filename=$(basename "$file")
    local basename_no_ext=$(echo "$filename" | sed 's/\.[^.]*$//')
    local file_ext=$(echo "$filename" | awk -F'.' '{print tolower($NF)}')

    # 移除常见的分辨率和状态后缀
    local clean_name=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//' | sed 's/_normal$//' | sed 's/_selected$//' | sed 's/_hover$//' | sed 's/_pressed$//' | sed 's/_disabled$//' | sed 's/_highlighted$//')

    # 检查是否为动画序列帧
    if is_animation_sequence_frame "$file" "$pod_dir"; then
        echo "ANIMATION:$filename" >> "$result_file"
        return 0
    fi

    # 增强: 检查是否为编译时生成的代码引用
    if check_generated_code_reference "$filename" "$pod_dir"; then
        echo "REFERENCED:$filename:Generated Code:$filename:编译时生成代码" >> "$result_file"
        return 0
    fi

    # 增强: 检查是否为系统图标引用
    if check_system_icon_reference "$filename" "$pod_dir"; then
        echo "REFERENCED:$filename:System Icon:$filename:系统图标" >> "$result_file"
        return 0
    fi

    # 构建搜索模式 - 增强版，确保不遗漏引用
    local search_patterns=(
        "$filename"
        "$basename_no_ext"
        "$clean_name"
        "${clean_name}_"
    )

    # 对于常见的iOS图片引用模式，去掉分辨率后缀后的基础名称
    local base_name_for_ios=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//')
    if [ "$base_name_for_ios" != "$basename_no_ext" ]; then
        search_patterns+=("$base_name_for_ios")
    fi

    # 增强：添加更多可能的引用模式
    # 1. 去掉常见状态后缀的模式
    local base_without_state=$(echo "$clean_name" | sed 's/_normal$//' | sed 's/_selected$//' | sed 's/_hover$//' | sed 's/_pressed$//' | sed 's/_disabled$//' | sed 's/_highlighted$//')
    if [ "$base_without_state" != "$clean_name" ]; then
        search_patterns+=("$base_without_state")
    fi

    # 2. 处理版本号后缀（如 _v2, _v3）
    local base_without_version=$(echo "$clean_name" | sed 's/_v[0-9]\+$//')
    if [ "$base_without_version" != "$clean_name" ]; then
        search_patterns+=("$base_without_version")
    fi

    # 3. 处理可能的前缀模式（如 icon_xxx 可能被引用为 xxx）
    if [[ "$clean_name" =~ ^(icon|img|image)_(.+)$ ]]; then
        local name_without_prefix="${BASH_REMATCH[2]}"
        if [ ${#name_without_prefix} -gt 4 ]; then  # 只有足够长的名称才添加
            search_patterns+=("$name_without_prefix")
        fi
    fi

    # 增强: 添加iOS特有的引用模式搜索
    # 1. 自定义图片加载框架模式（修复：添加项目特有的自定义方法）
    search_patterns+=("imageForKey:.*$clean_name")
    search_patterns+=("imageForKey:.*$basename_no_ext")
    search_patterns+=("imy_setImage:.*$clean_name")
    search_patterns+=("imy_setImage:.*$basename_no_ext")
    search_patterns+=("setImageWithName:.*$clean_name")
    search_patterns+=("setImageWithName:.*$basename_no_ext")

    # 2. 纯字符串匹配模式（修复：支持直接字符串赋值）
    search_patterns+=("@\"$clean_name\"")
    search_patterns+=("@\"$basename_no_ext\"")
    search_patterns+=("\"$clean_name\"")
    search_patterns+=("\"$basename_no_ext\"")

    # 3. Bundle路径引用模式
    search_patterns+=("pathForResource:.*$clean_name")
    search_patterns+=("pathForResource:.*$basename_no_ext")

    # 4. SwiftUI特殊引用模式
    search_patterns+=("Image(imy:.*$clean_name")
    search_patterns+=("Image(decorative:.*$clean_name")

    # 5. 文件路径引用模式
    search_patterns+=("contentsOfFile:.*$clean_name")
    search_patterns+=("contentsOfFile:.*$basename_no_ext")

    # 6. 变量赋值和常量定义模式（修复：支持更多字符串赋值方式）
    search_patterns+=("= @\"$clean_name\"")
    search_patterns+=("= @\"$basename_no_ext\"")
    search_patterns+=("= \"$clean_name\"")
    search_patterns+=("= \"$basename_no_ext\"")
    search_patterns+=("NSString.*@\"$clean_name\"")
    search_patterns+=("NSString.*@\"$basename_no_ext\"")
    search_patterns+=("String.*\"$clean_name\"")
    search_patterns+=("String.*\"$basename_no_ext\"")

    # 使用ripgrep（如果可用）或grep进行搜索
    local search_cmd="grep"
    local search_args="-r -l"
    
    if command -v rg >/dev/null 2>&1; then
        search_cmd="rg"
        search_args="--files-with-matches"
    fi
    
    # 构建文件类型过滤参数 - 增强版，支持更多iOS特有文件类型
    local file_includes=""
    if [ "$search_cmd" = "grep" ]; then
        file_includes="--include=*.m --include=*.h --include=*.swift --include=*.mm --include=*.xib --include=*.storyboard --include=*.plist --include=*.json --include=*.txt --include=*.strings --include=*.xml --include=*.css --include=*.js --include=*.ts --include=*.jsx --include=*.tsx --include=*.vue --include=*.dart --include=*.kt --include=*.java --include=*.cs --include=*.cpp --include=*.c --include=*.cc --include=*.rb --include=*.py --include=*.php --include=*.go --include=*.rs --include=*.scala --include=*.clj --include=*.sh --include=*.bat --include=*.ps1 --include=*.md --include=*.yml --include=*.yaml --include=*.toml --include=*.ini --include=*.cfg --include=*.conf --include=*.entitlements --include=*.xcconfig --exclude=Contents.json"
    else
        # 修复：ripgrep文件类型参数，明确添加对.m和.mm文件的支持
        file_includes="--type-add 'objcm:*.m' --type-add 'objcmm:*.mm' -t c -t cpp -t js -t ts -t py -t java -t swift -t objc -t objcm -t objcmm -t json -t xml -t yaml -t toml -t config"
    fi
    
    local found=false
    # 调试输出：显示搜索的模式
    # echo "DEBUG: 搜索文件 $filename，模式数量: ${#search_patterns[@]}" >&2

    for pattern in "${search_patterns[@]}"; do
        # echo "DEBUG: 搜索模式: '$pattern'" >&2

        if [ "$search_cmd" = "rg" ]; then
            # 修复: 使用新的文件类型定义，确保能检测到所有相关文件中的引用
            # 添加对.plist和.json文件的明确支持，同时使用新定义的objcm和objcmm类型
            if rg $search_args $file_includes --glob '*.plist' --glob '*.json' --glob '!Contents.json' -i "$pattern" "$pod_dir" >/dev/null 2>&1; then
                found=true
                local reference_files=$(rg $search_args $file_includes --glob '*.plist' --glob '*.json' --glob '!Contents.json' -i "$pattern" "$pod_dir" 2>/dev/null | head -3)
                echo "REFERENCED:$filename:$pattern:$(echo "$reference_files" | tr '\n' ' ')" >> "$result_file"
                # echo "DEBUG: 找到引用，模式: '$pattern'，文件: $reference_files" >&2
                break
            fi
        else
            # 增强grep搜索：添加忽略大小写选项，确保不遗漏引用
            if $search_cmd $search_args -i $file_includes "$pattern" "$pod_dir" >/dev/null 2>&1; then
                found=true
                local reference_files=$($search_cmd $search_args -i $file_includes "$pattern" "$pod_dir" 2>/dev/null | head -3)
                echo "REFERENCED:$filename:$pattern:$(echo "$reference_files" | tr '\n' ' ')" >> "$result_file"
                # echo "DEBUG: 找到引用，模式: '$pattern'，文件: $reference_files" >&2
                break
            fi
        fi
    done

    # 特殊处理：检查是否为 Asset Catalog 中的图片
    if [ "$found" = false ]; then
        local is_asset_catalog_image=false
        local file_dir=$(dirname "$file")

        # 检查是否在 Asset Catalog 相关目录中
        if [[ "$file_dir" == *.imageset ]] || [[ "$file_dir" == *.launchimage ]] || [[ "$file_dir" == *.complicationset ]] || [[ "$file_dir" == *.brandassets ]]; then
            # 提取 asset 名称（去掉后缀）
            local asset_name=$(basename "$file_dir")
            asset_name=${asset_name%.imageset}
            asset_name=${asset_name%.launchimage}
            asset_name=${asset_name%.complicationset}
            asset_name=${asset_name%.brandassets}

            # 在代码中搜索 Asset Catalog 引用，包括JSON文件和更多文件类型
            if [ "$search_cmd" = "rg" ]; then
                # 修复: 使用新的文件类型定义，确保Asset Catalog引用检测完整
                if rg --files-with-matches --type-add 'objcm:*.m' --type-add 'objcmm:*.mm' -t c -t cpp -t swift -t objc -t objcm -t objcmm -t json -t xml --glob '*.entitlements' --glob '*.xcconfig' --glob '!Contents.json' "$asset_name" "$pod_dir" >/dev/null 2>&1; then
                    is_asset_catalog_image=true
                    local asset_reference_files=$(rg --files-with-matches --type-add 'objcm:*.m' --type-add 'objcmm:*.mm' -t c -t cpp -t swift -t objc -t objcm -t objcmm -t json -t xml --glob '*.entitlements' --glob '*.xcconfig' --glob '!Contents.json' "$asset_name" "$pod_dir" 2>/dev/null | head -3)
                    echo "REFERENCED:$filename:Asset Catalog:$asset_name:$(echo "$asset_reference_files" | tr '\n' ' ')" >> "$result_file"
                fi
            else
                # 增强: 在grep中添加对更多文件类型的支持
                if grep -r -l --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" --include="*.xib" --include="*.storyboard" --include="*.json" --include="*.xml" --include="*.entitlements" --include="*.xcconfig" --exclude="Contents.json" "$asset_name" "$pod_dir" >/dev/null 2>&1; then
                    is_asset_catalog_image=true
                    local asset_reference_files=$(grep -r -l --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" --include="*.xib" --include="*.storyboard" --include="*.json" --include="*.xml" --include="*.entitlements" --include="*.xcconfig" --exclude="Contents.json" "$asset_name" "$pod_dir" 2>/dev/null | head -3)
                    echo "REFERENCED:$filename:Asset Catalog:$asset_name:$(echo "$asset_reference_files" | tr '\n' ' ')" >> "$result_file"
                fi
            fi
        fi

        # 检查是否在 .appiconset 目录中（App图标）
        if [[ "$file_dir" == *.appiconset ]] && [ "$is_asset_catalog_image" = false ]; then
            # App图标文件通常在 Contents.json 中被引用，应该被保护
            # 检查 Contents.json 文件是否存在并包含该文件名
            local contents_json="$file_dir/Contents.json"
            if [ -f "$contents_json" ]; then
                if grep -q "\"$filename\"" "$contents_json" 2>/dev/null; then
                    is_asset_catalog_image=true
                    echo "REFERENCED:$filename:App Icon:AppIcon:$contents_json" >> "$result_file"
                fi
            fi
        fi

        # 增强: 检查国际化资源 (.lproj 目录)
        if [[ "$file_dir" == *.lproj ]] && [ "$is_asset_catalog_image" = false ]; then
            # 国际化资源通常通过基础名称引用
            local base_name_without_lang=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//')
            if [ "$search_cmd" = "rg" ]; then
                # 修复: 使用新的文件类型定义检测国际化资源引用
                if rg --files-with-matches --type-add 'objcm:*.m' --type-add 'objcmm:*.mm' -t c -t cpp -t swift -t objc -t objcm -t objcmm -t json --glob '!*.lproj' "$base_name_without_lang" "$pod_dir" >/dev/null 2>&1; then
                    is_asset_catalog_image=true
                    local localized_reference_files=$(rg --files-with-matches --type-add 'objcm:*.m' --type-add 'objcmm:*.mm' -t c -t cpp -t swift -t objc -t objcm -t objcmm -t json --glob '!*.lproj' "$base_name_without_lang" "$pod_dir" 2>/dev/null | head -3)
                    echo "REFERENCED:$filename:Localized Resource:$base_name_without_lang:$(echo "$localized_reference_files" | tr '\n' ' ')" >> "$result_file"
                fi
            else
                if grep -r -l --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" --include="*.xib" --include="*.storyboard" --include="*.json" --exclude-dir="*.lproj" "$base_name_without_lang" "$pod_dir" >/dev/null 2>&1; then
                    is_asset_catalog_image=true
                    local localized_reference_files=$(grep -r -l --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" --include="*.xib" --include="*.storyboard" --include="*.json" --exclude-dir="*.lproj" "$base_name_without_lang" "$pod_dir" 2>/dev/null | head -3)
                    echo "REFERENCED:$filename:Localized Resource:$base_name_without_lang:$(echo "$localized_reference_files" | tr '\n' ' ')" >> "$result_file"
                fi
            fi
        fi

        # 增强: 检查Widget和Extension特殊引用
        if [ "$is_asset_catalog_image" = false ]; then
            # 检查是否在Widget或Extension目录中，这些通常有特殊的Bundle引用方式
            if [[ "$file_dir" == *Widget* ]] || [[ "$file_dir" == *Extension* ]] || [[ "$file_dir" == *Today* ]]; then
                # Widget和Extension可能通过Bundle路径引用图片
                local widget_patterns=(
                    "Bundle.*path.*$clean_name"
                    "Bundle.*path.*$basename_no_ext"
                    "Bundle(.*$clean_name"
                    "Bundle(.*$basename_no_ext"
                    "Image(imy:.*$clean_name"
                    "Image(imy:.*$basename_no_ext"
                )

                for widget_pattern in "${widget_patterns[@]}"; do
                    if [ "$search_cmd" = "rg" ]; then
                        # 修复: 使用新的文件类型定义检测Widget/Extension引用
                        if rg --files-with-matches --type-add 'objcm:*.m' --type-add 'objcmm:*.mm' -t swift -t objc -t objcm -t objcmm "$widget_pattern" "$pod_dir" >/dev/null 2>&1; then
                            is_asset_catalog_image=true
                            local widget_reference_files=$(rg --files-with-matches --type-add 'objcm:*.m' --type-add 'objcmm:*.mm' -t swift -t objc -t objcm -t objcmm "$widget_pattern" "$pod_dir" 2>/dev/null | head -3)
                            echo "REFERENCED:$filename:Widget/Extension:$widget_pattern:$(echo "$widget_reference_files" | tr '\n' ' ')" >> "$result_file"
                            break
                        fi
                    else
                        if grep -r -l --include="*.swift" --include="*.m" --include="*.h" --include="*.mm" -E "$widget_pattern" "$pod_dir" >/dev/null 2>&1; then
                            is_asset_catalog_image=true
                            local widget_reference_files=$(grep -r -l --include="*.swift" --include="*.m" --include="*.h" --include="*.mm" -E "$widget_pattern" "$pod_dir" 2>/dev/null | head -3)
                            echo "REFERENCED:$filename:Widget/Extension:$widget_pattern:$(echo "$widget_reference_files" | tr '\n' ' ')" >> "$result_file"
                            break
                        fi
                    fi
                done
            fi
        fi
        
        if [ "$is_asset_catalog_image" = false ]; then
            local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file")
            echo "UNUSED:$file:$size" >> "$result_file"
        fi
    fi
}

# 检测无引用图片文件
find_unused_images() {
    local pod_dir="$1"
    local unused_file=$(mktemp)

    log_info "检测无引用图片文件..."

    # 获取总文件数用于进度显示
    local total_files=$(scan_pod_images "$pod_dir" "all" | wc -l | tr -d ' ')
    echo "  将扫描 $total_files 个图片文件以检测引用..."

    # 检查是否支持并发处理
    local concurrent_jobs=$(get_concurrent_jobs)
    local use_parallel=$(check_parallel_support)
    
    echo "  使用 $use_parallel，并发数: $concurrent_jobs"

    # 创建临时结果文件
    local temp_result_file=$(mktemp)
    local temp_image_list=$(mktemp)
    
    # 生成图片文件列表
    scan_pod_images "$pod_dir" "all" > "$temp_image_list"
    
    # 导出必要的函数和变量给子进程使用
    export -f check_single_image_reference
    export -f is_animation_sequence_frame
    export -f check_dynamic_animation_reference
    export -f check_sequence_file_pattern
    export -f check_generated_code_reference
    export -f check_system_icon_reference
    export -f show_modern_progress
    export -f show_dynamic_progress
    export pod_dir
    export temp_result_file
    
    # 开始并发处理
    local start_time=$(date +%s)
    local progress_pid=""
    
    # 启动进度条显示
    show_dynamic_progress "扫描图片" &
    progress_pid=$!
    
    if [ "$use_parallel" = "parallel" ]; then
        # 使用GNU parallel
        cat "$temp_image_list" | parallel -j"$concurrent_jobs" --line-buffer \
            check_single_image_reference {} "$pod_dir" "$temp_result_file"
    elif [ "$use_parallel" = "xargs" ]; then
        # 使用xargs -P
        cat "$temp_image_list" | xargs -P"$concurrent_jobs" -I{} \
            bash -c 'check_single_image_reference "$1" "$2" "$3"' _ {} "$pod_dir" "$temp_result_file"
    else
        # 串行处理（回退方案）
        local current=0
        while read file; do
            check_single_image_reference "$file" "$pod_dir" "$temp_result_file"
            current=$((current + 1))
            # 每处理5个文件更新一次进度
            if [ $((current % 5)) -eq 0 ] || [ $current -eq $total_files ]; then
                show_modern_progress "$current" "$total_files" "扫描图片"
            fi
        done < "$temp_image_list"
        echo ""  # 换行确保输出完整
    fi
    
    # 停止进度条
    if [ -n "$progress_pid" ]; then
        kill $progress_pid 2>/dev/null || true
        wait $progress_pid 2>/dev/null || true
    fi
    
    # 清除进度条显示
    printf "\r%*s\r" "80" ""
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    echo "  检测完成，耗时: ${duration}秒"
    
    # 处理结果
    local unused_count=0
    local animation_count=0
    local referenced_count=0
    
    while IFS=':' read -r type data; do
        case "$type" in
            "UNUSED")
                # 格式: UNUSED:filepath:size
                local filepath=$(echo "$data" | cut -d':' -f1)
                local size=$(echo "$data" | cut -d':' -f2)
                echo "$filepath:$size" >> "$unused_file"
                unused_count=$((unused_count + 1))
                local filename=$(basename "$filepath")
                # 使用格式化的文件大小显示
                echo "    未引用: $filename ($(format_file_size $size))"
                ;;
            "ANIMATION")
                # 格式: ANIMATION:filename
                echo "    动画序列帧: $data (跳过检测)"
                animation_count=$((animation_count + 1))
                ;;
            "REFERENCED")
                # 格式: REFERENCED:filename:pattern:files
                local filename=$(echo "$data" | cut -d':' -f1)
                local pattern=$(echo "$data" | cut -d':' -f2)
                local files=$(echo "$data" | cut -d':' -f3-)
                echo "    引用发现: $filename -> 模式:'$pattern' 在文件: $files"
                referenced_count=$((referenced_count + 1))
                ;;
        esac
    done < "$temp_result_file"
    
    # 清理临时文件
    rm -f "$temp_result_file" "$temp_image_list"

    echo "  完成引用检测："
    echo "    - 无引用图片: $unused_count 个"
    echo "    - 有引用图片: $referenced_count 个" 
    echo "    - 动画序列帧: $animation_count 个"
    echo "    - 总计处理: $total_files 个"
    
    if [ -s "$unused_file" ]; then
        echo ""
        echo "发现无引用图片文件:"
        while IFS=':' read filepath size; do
            echo "  $(basename "$filepath") ($(($size / 1024))KB) - $filepath"
        done < "$unused_file"
        echo ""

        # 将详情保存到 pod_clean 工程目录下的 logs 目录
        local script_root_dir
        if [ -n "$SCRIPT_DIR" ]; then
            script_root_dir="$SCRIPT_DIR"
        else
            script_root_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
        fi
        local logs_dir="$script_root_dir/logs"
        mkdir -p "$logs_dir"
        local log_file="$logs_dir/unused_files_$(date +%Y%m%d_%H%M%S).txt"
        cp "$unused_file" "$log_file"
        echo "无引用文件详情已保存到: $log_file"
    else
        log_success "未发现无引用图片文件"
        rm -f "$unused_file"
    fi

    echo "$unused_file"
}

# 计算目录大小（递归计算所有文件）
calculate_directory_size() {
    local dir_path="$1"
    if [ ! -d "$dir_path" ]; then
        echo "0"
        return
    fi

    local total_size=0
    while IFS= read -r -d '' file; do
        if [ -f "$file" ]; then
            local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0")
            total_size=$((total_size + file_size))
        fi
    done < <(find "$dir_path" -type f -print0 2>/dev/null)

    echo "$total_size"
}

# 全局变量用于传递大数值，避免函数返回值限制
LAST_DELETION_SAVED=0
LAST_DELETION_COUNT=0

# 删除无引用和重复图片
delete_unused_images() {
    local pod_name="$1"
    local backup_dir="$2"
    local total_saved=0

    echo "  分析无引用图片..."

    # 仅查找无引用图片
    echo "  [DEBUG] 准备调用 find_unused_images('.')..."
    local unused_file
    unused_file=$(find_unused_images "." | tee /dev/stderr | tail -n1)
    echo "  [DEBUG] find_unused_images 返回: $unused_file"
    if [ -f "$unused_file" ]; then
        local candidate_count=$(wc -l < "$unused_file" 2>/dev/null || echo 0)
        echo "  [DEBUG] 未引用候选数量: $candidate_count"
    else
        echo "  [DEBUG] 未生成未引用候选列表文件"
    fi

    local delete_count=0
    # 用于跟踪已删除的 .imageset 目录，避免重复删除
    local deleted_imagesets=()

    # 处理无引用图片
    if [ -f "$unused_file" ] && [ -s "$unused_file" ]; then
        echo "  处理无引用图片..."

        # 先记录所有待删除文件到数据库
        echo "  记录删除进度到数据库..."
        while IFS=':' read filepath size; do
            if [ -f "$filepath" ]; then
                record_deletion_candidate "$pod_name" "$filepath"
            fi
        done < "$unused_file"

        # 执行删除操作
        while IFS=':' read filepath size; do
            if [ -f "$filepath" ]; then
                # 检查是否已经删除过
                if is_deletion_completed "$pod_name" "$filepath"; then
                    echo "    跳过已删除: $(basename "$filepath")"
                    continue
                fi

                local file_dir=$(dirname "$filepath")
                local filename=$(basename "$filepath")

                # 检查是否在 .imageset 或 .appiconset 目录中
                if [[ "$file_dir" == *.imageset ]] || [[ "$file_dir" == *.appiconset ]]; then
                    # 检查这个目录是否已经被删除
                    local asset_dir_already_deleted=false
                    for deleted_dir in "${deleted_imagesets[@]}"; do
                        if [ "$deleted_dir" = "$file_dir" ]; then
                            asset_dir_already_deleted=true
                            break
                        fi
                    done

                    if [ "$asset_dir_already_deleted" = true ]; then
                        echo "    跳过已删除的Asset目录: $(basename "$file_dir")"
                        # 仍然标记为已删除
                        mark_deletion_completed "$pod_name" "$filepath"
                        continue
                    fi

                    # 特殊处理：.appiconset 目录通常包含App图标，需要谨慎处理
                    if [[ "$file_dir" == *.appiconset ]]; then
                        echo "    ⚠️  警告: 检测到App图标文件 $filename 在 $(basename "$file_dir") 中"
                        echo "    ⚠️  App图标通常不应该被删除，请检查 Contents.json 引用"
                        echo "    ⚠️  跳过删除以确保App图标完整性"
                        # 不删除，直接跳过
                        continue
                    fi

                    # 计算整个 Asset 目录的大小
                    local asset_dir_size=$(calculate_directory_size "$file_dir")
                    local asset_dir_name=$(basename "$file_dir")

                    # 备份整个 Asset 目录
                    echo "    备份Asset目录: $asset_dir_name"
                    backup_file_preserve_structure "$file_dir" "$backup_dir"

                    # 删除整个 Asset 目录
                    rm -rf "$file_dir"
                    # 使用格式化的文件大小显示
                    echo "    删除: $asset_dir_name 目录 节省 $(format_file_size $asset_dir_size)"
                    total_saved=$((total_saved + asset_dir_size))
                    delete_count=$((delete_count + 1))

                    # 记录已删除的 Asset 目录
                    deleted_imagesets+=("$file_dir")

                    # 标记为已删除（这里标记的是原始文件路径）
                    mark_deletion_completed "$pod_name" "$filepath"

                    # 同时标记该 Asset 目录中的其他文件为已删除
                    # 这样后续遇到同一目录中的其他文件时就会跳过
                    while IFS=':' read other_filepath other_size; do
                        local other_file_dir=$(dirname "$other_filepath")
                        if [ "$other_file_dir" = "$file_dir" ] && [ "$other_filepath" != "$filepath" ]; then
                            mark_deletion_completed "$pod_name" "$other_filepath"
                        fi
                    done < "$unused_file"

                else
                    # 普通文件删除逻辑（非 .imageset 目录中的文件）
                    # 备份：保留原目录结构
                    backup_file_preserve_structure "$filepath" "$backup_dir"

                    # 删除
                    rm "$filepath"
                    # 使用格式化的文件大小显示
                    echo "    删除: $filename 节省 $(format_file_size $size)"
                    total_saved=$((total_saved + size))
                    delete_count=$((delete_count + 1))

                    # 标记为已删除
                    mark_deletion_completed "$pod_name" "$filepath"
                fi
            fi
        done < "$unused_file"

        rm -f "$unused_file"
    fi

    if [ $delete_count -gt 0 ]; then
        echo "  删除了 $delete_count 个无引用图片文件/目录"
    else
        echo "  没有发现可删除的图片文件"
    fi

    # 使用全局变量传递结果，避免返回值限制
    LAST_DELETION_SAVED=$total_saved
    LAST_DELETION_COUNT=$delete_count

    # 返回0表示成功
    return 0
}
